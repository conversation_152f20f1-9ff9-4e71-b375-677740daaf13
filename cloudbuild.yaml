steps:
  # ビルドステップ
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'asia-northeast1-docker.pkg.dev/$PROJECT_ID/zhangshuzhencom/web:latest',
      '-f', 'Dockerfile.web',
      '--cache-from', 'asia-northeast1-docker.pkg.dev/$PROJECT_ID/zhangshuzhencom/web:latest',
      '--build-arg', 'BUILDKIT_INLINE_CACHE=1',
      '.'
    ]

  # イメージをArtifact Registryにプッシュ
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'asia-northeast1-docker.pkg.dev/$PROJECT_ID/zhangshuzhencom/web:latest']

  # Cloud Runにデプロイ
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'zhangshuzhencom-web'
      - '--image=asia-northeast1-docker.pkg.dev/$PROJECT_ID/zhangshuzhencom/web:latest'
      - '--platform=managed'
      - '--region=asia-northeast1'
      - '--allow-unauthenticated'
      - '--set-env-vars=NODE_ENV=production,DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require,NEXTAUTH_SECRET=O6p0QRUgbhkx6g/dWfobPfsHKL/XMYrw00vttZq,NEXTAUTH_URL=https://zhangshuzhencom-web-288940727653.asia-northeast1.run.app'

# ビルドされるイメージの名前
images:
  - 'asia-northeast1-docker.pkg.dev/$PROJECT_ID/zhangshuzhencom/web:latest'

# ビルドリージョンの指定
options:
  logging: CLOUD_LOGGING_ONLY
  dynamic_substitutions: true
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: '100'