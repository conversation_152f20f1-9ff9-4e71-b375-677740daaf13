/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    formats: ['image/avif', 'image/webp'],
    domains: ['images.unsplash.com'],
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    unoptimized: false, // 画像最適化を有効化（ただしBase64画像は除く）
  },
  // 管理者ページを静的生成から除外
  output: 'standalone',
  // パフォーマンス最適化の設定
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
  },
  // i18n設定は削除（App Router互換性のため）
}

module.exports = nextConfig
