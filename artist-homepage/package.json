{"name": "artist-homepage", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:seed": "node prisma/seed.js"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@prisma/client": "6.7.0", "@types/bcryptjs": "^2.4.6", "@types/next-auth": "^3.13.0", "bcryptjs": "^3.0.2", "browser-image-compression": "^2.0.2", "date-fns": "^4.1.0", "dnd-core": "^16.0.1", "next": "^14.0.4", "next-auth": "^4.24.11", "pg": "^8.15.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icons": "^4.12.0", "sharp": "^0.34.1", "swiper": "^11.0.5", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}, "devDependencies": {"@types/node": "^20.17.32", "@types/pg": "^8.11.14", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.4", "postcss": "^8.4.0", "prisma": "6.7.0", "ts-node": "^10.9.2"}}