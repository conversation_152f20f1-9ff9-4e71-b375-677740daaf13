import Script from 'next/script';

interface JsonLdProps {
  type: 'Person' | 'WebSite' | 'Organization' | 'BreadcrumbList' | 'VisualArtwork' | 'Event';
  data: Record<string, any>;
}

export default function JsonLd({ type, data }: JsonLdProps) {
  return (
    <Script
      id={`json-ld-${type}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': type,
          ...data,
        }),
      }}
    />
  );
} 