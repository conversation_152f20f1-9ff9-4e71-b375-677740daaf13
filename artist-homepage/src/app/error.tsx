'use client';

import { useEffect } from 'react';
import Link from 'next/link';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // エラーをサーバーに報告することができます
    console.error('エラーが発生しました:', error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center h-screen bg-primary-50">
      <h1 className="text-5xl font-serif font-bold text-primary-700 mb-6">エラーが発生しました</h1>
      <p className="text-gray-600 mb-8">申し訳ありませんが、問題が発生しました。</p>
      <div className="flex space-x-4">
        <button
          className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors"
          onClick={reset}
        >
          もう一度試す
        </button>
        <Link 
          href="/" 
          className="px-6 py-3 border border-primary-600 text-primary-600 hover:bg-primary-50 rounded-md transition-colors"
        >
          トップページに戻る
        </Link>
      </div>
    </div>
  );
} 