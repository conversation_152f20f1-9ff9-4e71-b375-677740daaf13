import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'ニュース | Zhang Shuzhen',
  description: '張淑桢の展覧会情報、イベント告知、メディア掲載情報などの最新ニュースをお届けします。',
  openGraph: {
    title: 'ニュース | <PERSON> Shuzhen',
    description: '張淑桢の展覧会情報、イベント告知、メディア掲載情報などの最新ニュースをお届けします。',
    url: 'https://zhangshuzhen.com/news',
    images: [
      {
        url: '/images/news-og-image.jpg',
        width: 1200,
        height: 630,
        alt: '<PERSON> ニュース',
      }
    ],
    type: 'website',
  },
  alternates: {
    canonical: 'https://zhangshuzhen.com/news',
  },
};

export default function NewsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="news-page">
      {children}
    </div>
  )
}
