@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 60, 33, 26;
  --background-start-rgb: 247, 241, 233;
  --background-end-rgb: 237, 220, 200;
  --accent-rgb: 215, 145, 89;
}

/* 和紙風カード背景 */
@layer components {
  .washi-card {
    background-color: #fffdf7;
    background-image:
      radial-gradient(rgba(0,0,0,0.05) 1px, transparent 1px),
      radial-gradient(rgba(0,0,0,0.035) 1px, transparent 1px),
      linear-gradient(120deg, rgba(0,0,0,0.03) 25%, transparent 25% 75%, rgba(0,0,0,0.03) 75%),
      linear-gradient(0deg, rgba(255,255,255,0.92), rgba(255,255,255,0.92));
    background-size: 6px 6px, 10px 10px, 14px 14px, 100% 100%;
    background-position: 0 0, 1px 2px, 0 0, 0 0;
    border: 1px solid rgba(0,0,0,0.06);
    box-shadow: inset 0 0 0 1px rgba(255,255,255,0.6);
  }
}

/* 季節テーマ（アクセント色とボタン・リンクスタイル） */
[data-theme="spring"] {
  --accent-rgb: 217, 164, 91; /* 柔らかい金茶 */
  --accent-hover-rgb: 195, 140, 67;
  --accent-light-rgb: 240, 220, 180;
}
[data-theme="summer"] {
  --accent-rgb: 74, 163, 162; /* 青緑 */
  --accent-hover-rgb: 60, 135, 134;
  --accent-light-rgb: 180, 220, 219;
}
[data-theme="autumn"] {
  --accent-rgb: 184, 116, 58; /* 朽葉 */
  --accent-hover-rgb: 160, 92, 34;
  --accent-light-rgb: 220, 180, 140;
}
[data-theme="winter"] {
  --accent-rgb: 122, 138, 161; /* 墨藍 */
  --accent-hover-rgb: 98, 114, 137;
  --accent-light-rgb: 180, 190, 205;
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* カスタムトランジションとアニメーション */
@layer utilities {
  .transition-fade {
    transition-property: opacity, transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 500ms;
  }
  
  .fade-in {
    animation: fadeIn 0.8s ease-in-out forwards;
  }
  
  /* スクロールフェードイン用の初期状態 */
  .scroll-fade-hidden {
    opacity: 0;
    transform: translateY(20px);
  }
  
  /* モーション軽減設定時はアニメーションを無効化 */
  @media (prefers-reduced-motion: reduce) {
    .scroll-fade-hidden {
      opacity: 1;
      transform: none;
    }
  }
  
  /* ページ遷移時の滑らかな効果 */
  .page-transition {
    opacity: 0;
    transform: translateY(10px);
    animation: pageEnter 0.6s ease-out forwards;
  }
  
  @keyframes pageEnter {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @media (prefers-reduced-motion: reduce) {
    .page-transition {
      animation: none;
      opacity: 1;
      transform: none;
    }
  }
  
  .hover-lift {
    transition: transform 0.3s ease;
  }
  
  .hover-lift:hover {
    transform: translateY(-5px);
  }
  
  /* 季節テーマ対応ボタンスタイル */
  .btn-seasonal {
    background: rgb(var(--accent-rgb));
    color: white;
    transition: all 300ms ease;
  }
  .btn-seasonal:hover {
    background: rgb(var(--accent-hover-rgb));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--accent-rgb), 0.3);
  }
  
  /* 季節テーマ対応リンクスタイル */
  .link-seasonal {
    color: rgb(var(--accent-rgb));
    transition: color 200ms ease;
  }
  .link-seasonal:hover {
    color: rgb(var(--accent-hover-rgb));
  }
  
  /* 季節テーマ対応背景グラデーション */
  .bg-seasonal-gradient {
    background: linear-gradient(135deg, 
      rgba(var(--accent-light-rgb), 0.1), 
      rgba(var(--accent-rgb), 0.05)
    );
  }
  
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* 作品カードの魅力的なホバーエフェクト */
  @media (prefers-reduced-motion: no-preference) {
    .art-card {
      transform: translateZ(0);
      transition: transform 200ms ease, box-shadow 200ms ease;
      overflow: hidden;
    }
    .art-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .art-card-image {
      transition: transform 400ms ease, filter 300ms ease;
    }
    .art-card:hover .art-card-image {
      transform: scale(1.05);
      filter: brightness(1.1) saturate(1.1);
    }
    
    .art-card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(var(--accent-rgb), 0.1), rgba(var(--accent-rgb), 0.05));
      opacity: 0;
      transition: opacity 300ms ease;
    }
    .art-card:hover .art-card-overlay {
      opacity: 1;
    }
  }
}

/* 小鳥アニメーション（軽量・控えめ） */
.bird { position: absolute; transform-origin: center; }
/* 色指定（濃いグリーン / ライトブルー / 黄） */
.bird--1 { color: #1e5722; filter: drop-shadow(0 2px 2px rgba(0,0,0,.18)); top: -10px; left: -40px; animation: fly1 18s ease-in-out infinite; }
.bird--2 { color: #2f8eea; filter: drop-shadow(0 2px 2px rgba(0,0,0,.16)); top: 20px; right: -30px; animation: fly2 20s ease-in-out infinite; }
.bird--3 { color: #ffc400; filter: drop-shadow(0 2px 2px rgba(0,0,0,.16)); bottom: -20px; left: 30%; animation: fly3 22s ease-in-out infinite; }

@media (prefers-reduced-motion: reduce) {
  .bird { display: none; }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 軌道アニメーション */
@keyframes fly1 {
  0%   { transform: translate(0, 0) rotate(0deg) scale(0.95); opacity: .0 }
  10%  { opacity: .5 }
  50%  { transform: translate(140px, 50px) rotate(6deg) scale(1.06); }
  90%  { opacity: .5 }
  100% { transform: translate(0, 0) rotate(0deg) scale(0.95); opacity: .0 }
}
@keyframes fly2 {
  0%   { transform: translate(0, 0) rotate(0deg) scale(0.94); opacity: .0 }
  10%  { opacity: .45 }
  50%  { transform: translate(-160px, -40px) rotate(-8deg) scale(1.08); }
  90%  { opacity: .45 }
  100% { transform: translate(0, 0) rotate(0deg) scale(0.94); opacity: .0 }
}
@keyframes fly3 {
  0%   { transform: translate(0, 0) rotate(0deg) scale(0.96); opacity: .0 }
  10%  { opacity: .4 }
  50%  { transform: translate(120px, -30px) rotate(5deg) scale(1.04); }
  90%  { opacity: .4 }
  100% { transform: translate(0, 0) rotate(0deg) scale(0.96); opacity: .0 }
}

/* カスタムスクロールバー */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--background-start-rgb));
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--foreground-rgb), 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--foreground-rgb), 0.5);
}

/* 追加のマイクロインタラクション */
@layer utilities {
  /* ボタンのプレス効果 */
  .btn-press {
    transition: transform 0.1s ease;
  }
  .btn-press:active {
    transform: scale(0.98);
  }

  /* カードの浮き上がり効果 */
  .card-float {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  .card-float:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* テキストのフェードイン効果 */
  .text-fade-in {
    opacity: 0;
    transform: translateY(10px);
    animation: textFadeIn 0.6s ease-out forwards;
  }

  @keyframes textFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 画像のズーム効果 */
  .image-zoom {
    overflow: hidden;
  }
  .image-zoom img {
    transition: transform 0.4s ease;
  }
  .image-zoom:hover img {
    transform: scale(1.1);
  }

  /* グラデーション背景アニメーション */
  .gradient-animate {
    background: linear-gradient(-45deg,
      rgba(var(--accent-rgb), 0.1),
      rgba(var(--accent-light-rgb), 0.1),
      rgba(var(--accent-rgb), 0.05),
      rgba(var(--accent-light-rgb), 0.05)
    );
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
  }

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* パルス効果 */
  .pulse-subtle {
    animation: pulseSubtle 2s ease-in-out infinite;
  }

  @keyframes pulseSubtle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }
}
