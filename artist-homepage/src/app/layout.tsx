import type { Metadata } from 'next'
import { Noto_Sans_JP, Noto_Serif_JP, Zen_Kaku_Gothic_New } from 'next/font/google'
import './globals.css'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'
import Providers from '@/components/Providers'
import JsonLd from '@/components/JsonLd'

const notoSansJP = Noto_Sans_JP({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  display: 'swap',
  preload: false,
  variable: '--font-noto-sans',
})

const notoSerifJP = Noto_Serif_JP({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  display: 'swap',
  preload: false,
  variable: '--font-noto-serif',
})

const zenKakuGothicNew = Zen_Kaku_Gothic_New({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
  preload: false,
  variable: '--font-zen-kaku',
})

export const metadata: Metadata = {
  title: '<PERSON> | 张 淑桢',
  description: '鳥の声、風のかたち、土の記憶。誰の記憶にもない自然といにしえが語り合う世界を描く現代日本画アーティスト',
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32' },
      { url: '/icons/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/icons/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/icons/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/icons/safari-pinned-tab.png',
        color: '#d79159'
      }
    ]
  },
  manifest: '/manifest.json',
  appleWebApp: {
    title: 'Zhang Shuzhen | 张 淑桢',
    statusBarStyle: 'black-translucent',
  },
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
  themeColor: '#d79159',
  openGraph: {
    title: 'Zhang Shuzhen | 张 淑桢 - 現代日本画アーティスト',
    description: '鳥の声、風のかたち、土の記憶。誰の記憶にもない自然といにしえが語り合う世界を描く現代日本画アーティスト',
    url: 'https://zhangshuzhen.com',
    siteName: 'Zhang Shuzhen Official Website',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Zhang Shuzhen アート作品',
      }
    ],
    locale: 'ja_JP',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Zhang Shuzhen | 张 淑桢 - 現代日本画アーティスト',
    description: '鳥の声、風のかたち、土の記憶。誰の記憶にもない自然といにしえが語り合う世界を描く現代日本画アーティスト',
    images: ['/images/twitter-card.jpg'],
  },
  alternates: {
    canonical: 'https://zhangshuzhen.com',
  },
  keywords: '張淑桢, Zhang Shuzhen, 日本画, アーティスト, 現代美術, 絵画, 鳥, 自然, アート',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
      'max-video-preview': -1,
    }
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ja" className={`${notoSansJP.variable} ${notoSerifJP.variable} ${zenKakuGothicNew.variable}`}>
      <body className="font-sans overflow-x-hidden">
        <JsonLd
          type="WebSite"
          data={{
            name: 'Zhang Shuzhen | 张 淑桢 - Official Website',
            url: 'https://zhangshuzhen.com',
            potentialAction: {
              '@type': 'SearchAction',
              'target': 'https://zhangshuzhen.com/search?q={search_term_string}',
              'query-input': 'required name=search_term_string'
            }
          }}
        />
        <JsonLd
          type="Person"
          data={{
            name: 'Zhang Shuzhen',
            alternateName: '张 淑桢',
            jobTitle: '現代日本画アーティスト',
            url: 'https://zhangshuzhen.com',
            sameAs: [
              'https://www.instagram.com/zhangshuzhen_art/',
              'https://twitter.com/zhangshuzhenart'
            ],
            image: 'https://zhangshuzhen.com/images/profile.jpg',
            description: '鳥の声、風のかたち、土の記憶。誰の記憶にもない自然といにしえが語り合う世界を描く現代日本画アーティスト',
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': 'https://zhangshuzhen.com/about'
            }
          }}
        />
        <Providers>
          <div className="flex flex-col min-h-screen">
            <Navbar />
            <main className="flex-grow pt-16">
              {children}
            </main>
            <Footer />
          </div>
        </Providers>
      </body>
    </html>
  )
}
