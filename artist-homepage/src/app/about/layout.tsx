import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'プロフィール | Zhang Shuzhen',
  description: '現代日本画アーティスト張淑桢のプロフィール、経歴、受賞歴などをご紹介します。鳥の声、風のかたち、土の記憶を表現する作家です。',
  openGraph: {
    title: 'プロフィール | Zhang Shuzhen',
    description: '現代日本画アーティスト張淑桢のプロフィール、経歴、受賞歴などをご紹介します。鳥の声、風のかたち、土の記憶を表現する作家です。',
    url: 'https://zhangshuzhen.com/about',
    images: [
      {
        url: '/images/about-og-image.jpg',
        width: 1200,
        height: 630,
        alt: '<PERSON>zhen プロフィール',
      }
    ],
    type: 'website',
  },
  alternates: {
    canonical: 'https://zhangshuzhen.com/about',
  },
};

export default function AboutLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="about-page">
      {children}
    </div>
  )
}
