'use client';

import Image from 'next/image'
import dynamic from 'next/dynamic'
const AnimatedCharacterDynamic = dynamic(() => import('@/components/AnimatedCharacter'), { ssr: false })
import { useState, useEffect } from 'react';

interface ProfileData {
  profile_image: string | null;
  artist_bio: string | null;
  awards: string | null;
}

export default function AboutPage() {
  const [profileData, setProfileData] = useState<ProfileData>({
    profile_image: null,
    artist_bio: null,
    awards: null
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchProfileData() {
      try {
        console.log('Fetching profile data from client...');
        // キャッシュを無効化するためのオプションを追加
        const res = await fetch('/api/profile', {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (res.ok) {
          const data = await res.json();
          console.log('Profile data received:', {
            hasProfileImage: !!data.profile_image,
            hasArtistBio: !!data.artist_bio,
            hasAwards: !!data.awards
          });
          setProfileData(data);
        } else {
          console.error('Failed to fetch profile data:', res.status, res.statusText);
        }
      } catch (error) {
        console.error('Failed to fetch profile data:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchProfileData();
  }, []);

  // 受賞歴を行ごとに分割
  const awardsList = profileData.awards
    ? profileData.awards.split('\n').filter(line => line.trim() !== '')
    : [];

  return (
    <div className="container mx-auto px-4 py-8 page-transition">
      <h1 className="text-3xl font-bold mb-8 flex items-center gap-4">
        プロフィール
        <span className="inline-block">
          {/* クライアント側のみ描画（SSR影響回避） */}
          <AnimatedCharacterDynamic type="hamster" size="md" />
        </span>
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16">
        <div className="relative h-[400px] md:h-[500px] rounded-lg overflow-hidden">
          {profileData.profile_image && profileData.profile_image.trim() !== '' ? (
            <Image
              src={profileData.profile_image}
              alt={`${process.env.NEXT_PUBLIC_ARTIST_NAME || 'Zhang Shuzhen'} プロフィール写真`}
              fill
              style={{ objectFit: 'cover' }}
              priority
              className="rounded-lg"
              unoptimized={true} // Base64画像の場合は最適化をスキップ
            />
          ) : (
            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
              <p className="text-gray-400">プロフィール画像がありません</p>
            </div>
          )}
        </div>

        <div>
          <h2 className="text-2xl font-bold mb-2">
            <span><span className="text-green-800">Z</span>hang Shu<span className="text-red-600">z</span>hen | 张 淑桢</span>
          </h2>

          {/* アーティストステートメント */}
          {profileData.artist_bio && (
            <div className="prose mb-8">
              {profileData.artist_bio.split('\n\n').map((paragraph, index) => (
                <p key={index} className="mb-4">
                  {paragraph}
                </p>
              ))}
            </div>
          )}

          <div className="space-y-4 mb-8">
            <p>{process.env.NEXT_PUBLIC_ARTIST_ORIGIN}</p>
            <p>{process.env.NEXT_PUBLIC_ARTIST_EDUCATION}</p>
            <p>{process.env.NEXT_PUBLIC_ARTIST_PROFESSION}</p>
          </div>

          {awardsList.length > 0 && (
            <>
              <h3 className="text-xl font-semibold mt-8 mb-4">受賞歴</h3>
              <ul className="space-y-2">
                {awardsList.map((award, index) => {
                  // 年度と内容を分離（最初の空白で分割）
                  const parts = award.split(/\s+(.+)/);
                  const year = parts[0] || '';
                  const content = parts[1] || award;

                  return (
                    <li key={index} className="flex items-start">
                      <span className="font-medium w-16">{year}</span>
                      <span>{content}</span>
                    </li>
                  );
                })}
              </ul>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
