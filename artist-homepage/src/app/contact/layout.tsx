import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'お問い合わせ | Zhang Shuzhen',
  description: '張淑桢への作品制作依頼、展示会のご相談、メディア取材依頼など、各種お問い合わせはこちらのフォームからお願いします。',
  openGraph: {
    title: 'お問い合わせ | <PERSON> Shuzhen',
    description: '張淑桢への作品制作依頼、展示会のご相談、メディア取材依頼など、各種お問い合わせはこちらのフォームからお願いします。',
    url: 'https://zhangshuzhen.com/contact',
    images: [
      {
        url: '/images/contact-og-image.jpg',
        width: 1200,
        height: 630,
        alt: '<PERSON> お問い合わせ',
      }
    ],
    type: 'website',
  },
  alternates: {
    canonical: 'https://zhangshuzhen.com/contact',
  },
};

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="contact-page">
      {children}
    </div>
  )
} 