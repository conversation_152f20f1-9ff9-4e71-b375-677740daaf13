'use client'

import { useState, useEffect } from 'react'
import { getErrorMessage } from '@/lib/utils'

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState('')
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [contactEmail, setContactEmail] = useState('<EMAIL>')

  useEffect(() => {
    // 連絡先メールアドレスを取得
    const fetchContactEmail = async () => {
      try {
        const res = await fetch('/api/social-media')
        if (res.ok) {
          const data = await res.json()
          // データが配列であることを確認
          if (Array.isArray(data)) {
            // 大文字小文字を区別せずにemailプラットフォームを探す
            const emailSetting = data.find(item =>
              item &&
              typeof item === 'object' &&
              item.platform &&
              typeof item.platform === 'string' &&
              item.platform.toLowerCase() === 'email'
            )

            // メールアドレスが見つかった場合
            if (emailSetting && emailSetting.url) {
              setContactEmail(emailSetting.url)
            }
          }
        }
      } catch (error: unknown) {
        console.error('Failed to fetch contact email:', getErrorMessage(error, 'メールアドレスの取得に失敗しました'))
        // エラーが発生した場合はデフォルトのメールアドレスを使用
      }
    }

    fetchContactEmail()
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // エラーメッセージをクリア
    setSubmitError('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitError('')
    setSubmitSuccess(false)

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'メッセージの送信に失敗しました')
      }

      // 送信成功
      setSubmitSuccess(true)
      setFormData({
        name: '',
        email: '',
        message: ''
      })
    } catch (error: unknown) {
      console.error('Submit error:', error)
      setSubmitError(getErrorMessage(error, 'メッセージの送信に失敗しました。後でもう一度お試しください。'))
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">お問い合わせ</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">ご連絡</h2>
          <p className="mb-4">ご質問やお問い合わせがございましたら、フォームまたはメールにてお気軽にご連絡ください。</p>

          <div className="mt-6">
            <h3 className="font-medium mb-2">メール</h3>
            <p className="text-gray-600">{contactEmail}</p>
          </div>
        </div>

        <div>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                お名前
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                メールアドレス
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                メッセージ
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
                rows={5}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              ></textarea>
            </div>

            {submitError && (
              <div className="text-red-600 text-sm mb-4">
                {submitError}
              </div>
            )}

            {submitSuccess && (
              <div className="text-green-600 text-sm mb-4">
                メッセージをお送りいただきありがとうございます。近日中にご連絡いたします。
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
                  isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? '送信中...' : 'Send Message'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
