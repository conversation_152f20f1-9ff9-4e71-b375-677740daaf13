'use client';

import { useEffect } from 'react';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // グローバルエラーをサーバーに報告することができます
    console.error('致命的なエラーが発生しました:', error);
  }, [error]);

  return (
    <html lang="ja">
      <body>
        <div className="flex flex-col items-center justify-center h-screen bg-primary-50">
          <h1 className="text-5xl font-serif font-bold text-red-700 mb-6">致命的なエラー</h1>
          <p className="text-gray-600 mb-8">申し訳ありませんが、サイトの読み込み中に問題が発生しました。</p>
          <button
            className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-md transition-colors"
            onClick={reset}
          >
            もう一度試す
          </button>
        </div>
      </body>
    </html>
  );
} 