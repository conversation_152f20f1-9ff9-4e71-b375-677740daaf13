import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// ブログ記事の詳細取得
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const post = await prisma.blogPost.findUnique({
      where: { id }
    });

    if (!post) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(post);
  } catch (error: unknown) {
    console.error('Failed to fetch blog post:', error instanceof Error ? error.message : String(error));
    return NextResponse.json(
      { error: 'Failed to fetch blog post' },
      { status: 500 }
    );
  }
}

// ブログ記事の更新
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // 認証チェック
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = parseInt(params.id);
    const data = await request.json();

    // バリデーション
    if (!data.title || !data.content || !data.posted_at) {
      return NextResponse.json(
        { error: 'Title, content and posted date are required' },
        { status: 400 }
      );
    }

    const post = await prisma.blogPost.update({
      where: { id },
      data: {
        title: data.title,
        content: data.content,
        image_data: data.image_data,
        thumbnail_data: data.thumbnail_data,
        url: data.url,
        posted_at: new Date(data.posted_at),
        is_active: data.is_active ?? true,
        is_featured: data.is_featured ?? false,
      }
    });

    return NextResponse.json(post);
  } catch (error: unknown) {
    console.error('Failed to update blog post:', error instanceof Error ? error.message : String(error));
    return NextResponse.json(
      { error: 'Failed to update blog post' },
      { status: 500 }
    );
  }
}

// ブログ記事の削除
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // 認証チェック
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = parseInt(params.id);
    await prisma.blogPost.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Blog post deleted successfully' });
  } catch (error: unknown) {
    console.error('Failed to delete blog post:', error instanceof Error ? error.message : String(error));
    return NextResponse.json(
      { error: 'Failed to delete blog post' },
      { status: 500 }
    );
  }
}