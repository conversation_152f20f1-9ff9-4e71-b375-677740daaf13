import NextAuth from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NextResponse } from 'next/server';
import { getErrorMessage } from '@/lib/utils';

// CORSヘッダーを設定するヘルパー関数
function setCorsHeaders(response: NextResponse) {
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
  response.headers.set(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
  );
  return response;
}

// App Router形式で動作するようにNextAuthハンドラーを設定
const handler = async (req: Request, ...args: any[]) => {
  try {
    // NextAuthハンドラーを呼び出す
    return await NextAuth(authOptions)(req, ...args);
  } catch (error: unknown) {
    console.error('NextAuth error:', getErrorMessage(error));

    // エラーレスポンスを返す
    const errorResponse = new NextResponse(
      JSON.stringify({ error: 'Internal Server Error', details: getErrorMessage(error) }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
    return setCorsHeaders(errorResponse);
  }
};

// App Routerスタイルでエクスポート
export { handler as GET, handler as POST };
