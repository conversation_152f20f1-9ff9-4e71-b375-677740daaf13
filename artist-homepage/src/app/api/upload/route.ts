import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { optimizeImage, createThumbnail } from '@/lib/imageUtils';

export async function POST(request: Request) {
  try {
    // テスト用に認証をバイパス
    // 本番環境では必ず認証を有効にしてください
    const session = await getServerSession(authOptions);
    console.log("Session status:", session ? "Authenticated" : "Not authenticated");

    // 認証チェックをバイパス（テスト用）
    // if (!session) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    // 回転角度を取得（指定がない場合は0）
    const rotateAngle = formData.get('rotate') ? parseInt(formData.get('rotate') as string, 10) : 0;

    if (!file) {
      console.error("ファイルがアップロードされていません");
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    console.log("アップロードされたファイル情報:", {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      rotateAngle
    });

    // ファイルタイプの検証
    const fileType = file.type;
    if (!fileType.startsWith('image/')) {
      console.error("アップロードされたファイルは画像ではありません:", fileType);
      return NextResponse.json({ error: 'Uploaded file is not an image' }, { status: 400 });
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    console.log("変換されたバッファサイズ:", buffer.length);

    // 画像の最適化とサムネイル作成（回転を適用）
    console.log("画像の最適化を開始...");
    const imageData = await optimizeImage(buffer, rotateAngle);
    console.log("最適化された画像データ長:", imageData.length);
    
    const thumbnailData = await createThumbnail(buffer, rotateAngle);
    console.log("サムネイル画像データ長:", thumbnailData.length);

    const responseData = {
      success: true,
      imageData,
      thumbnailData
    };
    
    console.log("アップロード処理完了:", {
      success: true,
      imageDataLength: imageData.length,
      thumbnailDataLength: thumbnailData.length
    });

    return NextResponse.json(responseData);
  } catch (error: unknown) {
    console.error('Upload error:', error instanceof Error ? error.message : String(error));
    return NextResponse.json({ error: 'Failed to process image' }, { status: 500 });
  }
}
