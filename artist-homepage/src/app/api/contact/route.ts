import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, message } = body;

    // 入力検証
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: '名前、メールアドレス、メッセージは必須です' },
        { status: 400 }
      );
    }

    // メッセージの保存
    const contactMessage = await prisma.contactMessage.create({
      data: {
        name,
        email,
        message,
      }
    });

    return NextResponse.json({ success: true, id: contactMessage.id });
  } catch (error: unknown) {
    console.error('Database error:', error instanceof Error ? error.message : String(error));
    return NextResponse.json(
      { error: 'メッセージの送信に失敗しました' },
      { status: 500 }
    );
  }
}
