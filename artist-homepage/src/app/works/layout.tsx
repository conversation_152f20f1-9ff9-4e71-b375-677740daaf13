import { Metadata } from 'next';

export const metadata: Metadata = {
  title: '作品集 | Zhang Shuzhen',
  description: '張淑桢の作品集です。現代日本画アーティストとして、鳥の声、風のかたち、土の記憶を表現します。',
  openGraph: {
    title: '作品集 | <PERSON> Shuzhen',
    description: '張淑桢の作品集です。現代日本画アーティストとして、鳥の声、風のかたち、土の記憶を表現します。',
    url: 'https://zhangshuzhen.com/works',
    images: [
      {
        url: '/images/works-og-image.jpg', 
        width: 1200,
        height: 630,
        alt: '<PERSON> 作品集',
      }
    ],
    type: 'website',
  },
  alternates: {
    canonical: 'https://zhangshuzhen.com/works',
  },
};

export default function WorksLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="works-page">
      {children}
    </div>
  )
} 