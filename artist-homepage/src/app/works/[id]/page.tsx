'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import AspectRatioImage from '@/components/AspectRatioImage';
import JsonLd from '@/components/JsonLd';

interface Work {
  id: string;
  title: string;
  year: string;
  description: string;
  category: string;
  materials: string;
  dimensions: string;
  tags: string[];
  image_data: string;
  thumbnail_data: string;
  image_url: string;
  medium: string;
  creation_year: string;
  width?: number;
  height?: number;
}

export default function WorkDetailPage() {
  const params = useParams();
  const id = params.id as string;

  const [work, setWork] = useState<Work | null>(null);
  const [relatedWorks, setRelatedWorks] = useState<Work[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // カテゴリの日本語表示
  const getCategoryLabel = (category: string) => {
    const categoryMap: { [key: string]: string } = {
      'painting': '絵画',
      'sculpture': '彫刻',
      'photography': '写真',
      'digital': 'デジタル',
      'other': 'その他'
    };
    return categoryMap[category] || category;
  };

  useEffect(() => {
    async function fetchWork() {
      try {
        // 作品の詳細を取得
        const res = await fetch(`/api/works/${id}`);
        if (!res.ok) throw new Error('Failed to fetch work');
        const data = await res.json();
        setWork(data);

        // 関連作品を取得
        const relatedRes = await fetch('/api/works');
        if (!relatedRes.ok) throw new Error('Failed to fetch related works');
        const relatedData = await relatedRes.json();

        // 現在の作品を除外し、最大3件を取得
        const filtered = relatedData
          .filter((w: Work) => w.id.toString() !== id)
          .slice(0, 3);

        setRelatedWorks(filtered);
      } catch (error) {
        console.error('Fetch error:', error);
        setError('作品の取得に失敗しました。');
      } finally {
        setLoading(false);
      }
    }

    fetchWork();
  }, [id]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-10">Loading...</div>
      </div>
    );
  }

  if (error || !work) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-3xl font-bold mb-4">作品が見つかりません</h1>
        <p className="mb-8">お探しの作品は存在しないか、削除された可能性があります。</p>
        <Link
          href="/works"
          className="inline-block bg-indigo-600 text-white py-2 px-6 rounded-md hover:bg-indigo-700 transition-colors"
        >
          ギャラリーに戻る
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-12">
      {/* JSON-LD構造化データ */}
      {work && (
        <JsonLd
          type="VisualArtwork"
          data={{
            name: work.title,
            creator: {
              '@type': 'Person',
              name: 'Zhang Shuzhen',
              alternateName: '张 淑桢',
              url: 'https://zhangshuzhen.com/about'
            },
            description: work.description,
            image: work.image_url,
            artMedium: work.medium,
            artform: '日本画',
            dateCreated: work.creation_year,
            width: work.width ? {
              '@type': 'QuantitativeValue',
              value: work.width,
              unitText: 'cm'
            } : undefined,
            height: work.height ? {
              '@type': 'QuantitativeValue',
              value: work.height,
              unitText: 'cm'
            } : undefined,
            url: `https://zhangshuzhen.com/works/${params.id}`
          }}
        />
      )}
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/works" className="text-indigo-600 hover:text-indigo-800 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            ギャラリーに戻る
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div className="h-[500px] lg:h-[600px]">
            <AspectRatioImage
              src={work.image_data}
              alt={work.title}
              containerClassName="rounded-lg h-full"
            />
          </div>

          <div>
            <h1 className="text-3xl font-bold mb-2">{work.title}</h1>
            <p className="text-gray-600 mb-6">{work.year}</p>

            <div className="mb-8">
              <p className="text-lg mb-6">{work.description}</p>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-gray-700">カテゴリ</h3>
                  <p>{getCategoryLabel(work.category)}</p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-700">素材</h3>
                  <p>{work.materials || '記載なし'}</p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-700">サイズ</h3>
                  <p>{work.dimensions || '記載なし'}</p>
                </div>
              </div>
            </div>

            {work.tags && work.tags.length > 0 && (
              <div className="mb-8">
                <h3 className="font-semibold text-gray-700 mb-2">タグ</h3>
                <div className="flex flex-wrap gap-2">
                  {work.tags.map(tag => (
                    <span
                      key={tag}
                      className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="mt-12">
              <h3 className="font-semibold text-gray-700 mb-4">この作品についてのお問い合わせ</h3>
              <Link
                href="/contact"
                className="inline-block bg-indigo-600 text-white py-2 px-6 rounded-md hover:bg-indigo-700 transition-colors"
              >
                お問い合わせをする
              </Link>
            </div>
          </div>
        </div>

        {relatedWorks.length > 0 && (
          <div className="mt-16">
            <h2 className="text-2xl font-bold mb-8">関連作品</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedWorks.map(relatedWork => (
                <Link key={relatedWork.id} href={`/works/${relatedWork.id}`} className="block group">
                  <div className="mb-2 overflow-hidden rounded-lg">
                    <AspectRatioImage
                      src={relatedWork.thumbnail_data}
                      alt={relatedWork.title}
                      containerClassName="transition-transform group-hover:scale-105"
                    />
                  </div>
                  <h3 className="font-semibold group-hover:text-indigo-600">{relatedWork.title}</h3>
                  <p className="text-gray-600">{relatedWork.year}</p>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
