/**
 * Format a date to a readable string
 * @param date Date to format
 * @returns Formatted date string
 */
export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('ja-JP', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Truncate a string to a specified length
 * @param str String to truncate
 * @param length Maximum length
 * @returns Truncated string
 */
export function truncateString(str: string, length: number): string {
  if (str.length <= length) return str;
  return str.slice(0, length) + '...';
}

/**
 * Get environment variable
 * @param key Environment variable key
 * @param defaultValue Default value if not found
 * @returns Environment variable value
 */
export function getEnv(key: string, defaultValue: string = ''): string {
  const value = process.env[key];
  return value || defaultValue;
}

/**
 * エラーから安全にメッセージを抽出するヘルパー関数
 * TypeScript 4.0以降でcatch句のerrorがunknown型になったことに対応
 * 
 * @param error キャッチされたエラー
 * @param fallbackMessage フォールバックメッセージ
 * @returns エラーメッセージ
 */
export function getErrorMessage(error: unknown, fallbackMessage: string = '不明なエラーが発生しました'): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string') {
    return error.message;
  }
  
  return fallbackMessage;
}
