steps:
  # ビルドステージ
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'asia-northeast1-docker.pkg.dev/$PROJECT_ID/zhangshuzhencom/web:latest', '.']

  # イメージをプッシュ
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'asia-northeast1-docker.pkg.dev/$PROJECT_ID/zhangshuzhencom/web:latest']

  # Cloud Runにデプロイ
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'zhangshuzhencom-web'
      - '--image=asia-northeast1-docker.pkg.dev/$PROJECT_ID/zhangshuzhencom/web:latest'
      - '--region=asia-northeast1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--set-env-vars=NEXTAUTH_URL=https://zhangshuzhencom-web-288940727653.asia-northeast1.run.app,NEXTAUTH_SECRET=O6p0QRUgbhkx6g/dWfobPfsHKL/XMYrw00vttZq,NODE_ENV=production,DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require'

images:
  - 'asia-northeast1-docker.pkg.dev/$PROJECT_ID/zhangshuzhencom/web:latest'

timeout: '1800s'
options:
  machineType: 'E2_HIGHCPU_8'
  region: 'asia-northeast1'

# リージョンを明示的に指定
# 注意: このプロパティはCloud Buildのリージョンを指定するものではなく、
# コメントとして残しています。実際のリージョン指定はコマンドラインで行います。
# region: 'asia-northeast1'
