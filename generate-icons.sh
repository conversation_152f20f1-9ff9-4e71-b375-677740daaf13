#!/bin/bash

# 透過PNG画像から各サイズのアイコンを生成するスクリプト
# ImageMagickが必要です

# 元画像のパス
SOURCE_IMG="/Users/<USER>/Projects/zhangshuzhencom/doc/Favicon.png"

# 出力先ディレクトリ
OUTPUT_DIR="artist-homepage/public/icons"

# ディレクトリが存在しない場合は作成
mkdir -p "$OUTPUT_DIR"

# 各サイズのPNG画像を生成（透過を維持）
convert "$SOURCE_IMG" -resize 16x16 -background none "$OUTPUT_DIR/favicon-16x16.png"
convert "$SOURCE_IMG" -resize 32x32 -background none "$OUTPUT_DIR/favicon-32x32.png"
convert "$SOURCE_IMG" -resize 192x192 -background none "$OUTPUT_DIR/android-chrome-192x192.png"
convert "$SOURCE_IMG" -resize 512x512 -background none "$OUTPUT_DIR/android-chrome-512x512.png"
convert "$SOURCE_IMG" -resize 180x180 -background none "$OUTPUT_DIR/apple-touch-icon.png"
convert "$SOURCE_IMG" -resize 512x512 -background none "$OUTPUT_DIR/safari-pinned-tab.png"

# favicon.icoを生成（複数サイズを含む）
convert "$SOURCE_IMG" -resize 16x16 -background none \
        "$SOURCE_IMG" -resize 32x32 -background none \
        "$SOURCE_IMG" -resize 48x48 -background none \
        "$SOURCE_IMG" -resize 64x64 -background none \
        "$OUTPUT_DIR/favicon.ico"

echo "全てのアイコンが正常に生成されました" 