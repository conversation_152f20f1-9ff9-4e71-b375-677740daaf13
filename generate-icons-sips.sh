#!/bin/bash

# 透過PNG画像から各サイズのアイコンを生成するスクリプト（sips使用）
# macOS標準コマンド使用

# 元画像のパス
SOURCE_IMG="/Users/<USER>/Projects/zhangshuzhencom/doc/Favicon.png"

# 出力先ディレクトリ
OUTPUT_DIR="artist-homepage/public/icons"

# ディレクトリが存在しない場合は作成
mkdir -p "$OUTPUT_DIR"

# 各サイズのPNG画像を生成（透過を維持）
for size in 16 32 192 512 180; do
  output_file=""
  
  case $size in
    16)
      output_file="$OUTPUT_DIR/favicon-16x16.png"
      ;;
    32)
      output_file="$OUTPUT_DIR/favicon-32x32.png"
      ;;
    192)
      output_file="$OUTPUT_DIR/android-chrome-192x192.png"
      ;;
    512)
      if [ ! -f "$OUTPUT_DIR/android-chrome-512x512.png" ]; then
        output_file="$OUTPUT_DIR/android-chrome-512x512.png"
      else
        output_file="$OUTPUT_DIR/safari-pinned-tab.png"
      fi
      ;;
    180)
      output_file="$OUTPUT_DIR/apple-touch-icon.png"
      ;;
  esac
  
  # ファイルをコピーして処理
  cp "$SOURCE_IMG" "$output_file"
  sips --resampleHeightWidth $size $size "$output_file" > /dev/null 2>&1
  
  echo "生成: $output_file ($size x $size)"
done

echo "favicon.icoの生成にはImageMagickが必要です。インストール後に以下のコマンドを実行してください："
echo "convert \"$SOURCE_IMG\" -resize 16x16 -background none -resize 32x32 -background none \"$OUTPUT_DIR/favicon.ico\""

echo "サイズ変更済みのPNG画像が生成されました。" 