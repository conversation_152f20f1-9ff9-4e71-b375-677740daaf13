<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>アニメーションふくろう</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5e6d3, #e8d5c4);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
        }
        
        .container {
            text-align: center;
        }
        
        .owl-container {
            display: inline-block;
            filter: drop-shadow(0 10px 20px rgba(0,0,0,0.1));
        }
        
        h1 {
            color: #8b6f47;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦉 可愛いふくろう 🦉</h1>
        <div class="owl-container">
            <svg width="300" height="350" viewBox="0 0 300 350">
                <!-- 影 -->
                <ellipse cx="150" cy="320" rx="60" ry="15" fill="rgba(0,0,0,0.2)">
                    <animateTransform 
                        attributeName="transform" 
                        type="scale" 
                        values="1,1;1.1,1;1,1" 
                        dur="3s" 
                        repeatCount="indefinite"/>
                </ellipse>
                
                <!-- 足 -->
                <g id="feet">
                    <!-- 左足 -->
                    <g transform="translate(120, 280)">
                        <ellipse cx="0" cy="0" rx="12" ry="8" fill="#d2691e">
                            <animateTransform 
                                attributeName="transform" 
                                type="rotate" 
                                values="0;-10;5;0" 
                                dur="2s" 
                                repeatCount="indefinite"/>
                        </ellipse>
                        <!-- 爪 -->
                        <path d="M-8,-3 Q-12,-8 -10,-5 M0,-5 Q0,-10 2,-7 M8,-3 Q12,-8 10,-5" 
                              stroke="#b8560f" stroke-width="2" fill="none"/>
                    </g>
                    
                    <!-- 右足 -->
                    <g transform="translate(180, 285)">
                        <ellipse cx="0" cy="0" rx="12" ry="8" fill="#d2691e">
                            <animateTransform 
                                attributeName="transform" 
                                type="rotate" 
                                values="0;10;-5;0" 
                                dur="2.5s" 
                                repeatCount="indefinite"/>
                        </ellipse>
                        <!-- 爪 -->
                        <path d="M-8,-3 Q-12,-8 -10,-5 M0,-5 Q0,-10 2,-7 M8,-3 Q12,-8 10,-5" 
                              stroke="#b8560f" stroke-width="2" fill="none"/>
                    </g>
                </g>
                
                <!-- 体 -->
                <g id="body">
                    <ellipse cx="150" cy="200" rx="80" ry="90" fill="#cd853f" stroke="#8b4513" stroke-width="4"/>
                    
                    <!-- お腹 -->
                    <ellipse cx="150" cy="220" rx="50" ry="60" fill="#f5deb3"/>
                    
                    <!-- お腹の模様 -->
                    <g fill="#cd853f">
                        <path d="M 130 200 Q 140 205 150 200 Q 160 205 170 200" stroke="#cd853f" stroke-width="2" fill="none"/>
                        <path d="M 135 220 Q 145 225 155 220" stroke="#cd853f" stroke-width="2" fill="none"/>
                        <path d="M 125 240 Q 135 245 145 240" stroke="#cd853f" stroke-width="2" fill="none"/>
                        <path d="M 155 240 Q 165 245 175 240" stroke="#cd853f" stroke-width="2" fill="none"/>
                        <path d="M 140 260 Q 150 265 160 260" stroke="#cd853f" stroke-width="2" fill="none"/>
                    </g>
                </g>
                
                <!-- 頭（アニメーション付き） -->
                <g id="head">
                    <animateTransform 
                        attributeName="transform" 
                        type="rotate" 
                        values="0 150 120;-8 150 120;5 150 120;0 150 120;3 150 120;-5 150 120;0 150 120" 
                        dur="6s" 
                        repeatCount="indefinite"/>
                    
                    <!-- 頭の本体 -->
                    <circle cx="150" cy="120" r="70" fill="#cd853f" stroke="#8b4513" stroke-width="4"/>
                    
                    <!-- 耳 -->
                    <g id="ears">
                        <!-- 左耳 -->
                        <path d="M 100 80 Q 90 60 110 70 Q 120 75 115 85 Z" fill="#cd853f" stroke="#8b4513" stroke-width="3"/>
                        <path d="M 105 75 Q 100 65 110 70 Q 115 72 112 78 Z" fill="#deb887"/>
                        
                        <!-- 右耳 -->
                        <path d="M 200 80 Q 210 60 190 70 Q 180 75 185 85 Z" fill="#cd853f" stroke="#8b4513" stroke-width="3"/>
                        <path d="M 195 75 Q 200 65 190 70 Q 185 72 188 78 Z" fill="#deb887"/>
                    </g>
                    
                    <!-- 目 -->
                    <g id="eyes">
                        <!-- 左目の白い部分 -->
                        <circle cx="125" cy="110" r="22" fill="#f5f5dc" stroke="#8b4513" stroke-width="2"/>
                        <!-- 右目の白い部分 -->
                        <circle cx="175" cy="110" r="22" fill="#f5f5dc" stroke="#8b4513" stroke-width="2"/>
                        
                        <!-- 瞳 -->
                        <g id="pupils">
                            <circle cx="125" cy="110" r="12" fill="#2f1b14">
                                <animate attributeName="cy" values="110;108;110" dur="4s" repeatCount="indefinite"/>
                            </circle>
                            <circle cx="175" cy="110" r="12" fill="#2f1b14">
                                <animate attributeName="cy" values="110;108;110" dur="4s" repeatCount="indefinite"/>
                            </circle>
                            
                            <!-- ハイライト -->
                            <circle cx="130" cy="105" r="4" fill="white"/>
                            <circle cx="180" cy="105" r="4" fill="white"/>
                        </g>
                        
                        <!-- まばたき -->
                        <g id="blink">
                            <ellipse cx="125" cy="110" rx="22" ry="0" fill="#cd853f" opacity="0">
                                <animate attributeName="ry" values="0;22;0" dur="0.3s" begin="0s;5s;8s;12s" fill="freeze"/>
                                <animate attributeName="opacity" values="0;1;0" dur="0.3s" begin="0s;5s;8s;12s" fill="freeze"/>
                            </ellipse>
                            <ellipse cx="175" cy="110" rx="22" ry="0" fill="#cd853f" opacity="0">
                                <animate attributeName="ry" values="0;22;0" dur="0.3s" begin="0s;5s;8s;12s" fill="freeze"/>
                                <animate attributeName="opacity" values="0;1;0" dur="0.3s" begin="0s;5s;8s;12s" fill="freeze"/>
                            </ellipse>
                        </g>
                    </g>
                    
                    <!-- くちばし -->
                    <g id="beak">
                        <path d="M 150 130 L 145 140 L 155 140 Z" fill="#ff8c00" stroke="#d2691e" stroke-width="2">
                            <animateTransform 
                                attributeName="transform" 
                                type="scale" 
                                values="1;1.1;1" 
                                dur="3s" 
                                repeatCount="indefinite"/>
                        </path>
                    </g>
                </g>
                
                <!-- 羽ばたくような微細な動き -->
                <g id="wings" opacity="0.3">
                    <ellipse cx="100" cy="180" rx="15" ry="40" fill="#8b4513">
                        <animateTransform 
                            attributeName="transform" 
                            type="rotate" 
                            values="0 100 180;-15 100 180;0 100 180" 
                            dur="4s" 
                            repeatCount="indefinite"/>
                    </ellipse>
                    <ellipse cx="200" cy="180" rx="15" ry="40" fill="#8b4513">
                        <animateTransform 
                            attributeName="transform" 
                            type="rotate" 
                            values="0 200 180;15 200 180;0 200 180" 
                            dur="4s" 
                            repeatCount="indefinite"/>
                    </ellipse>
                </g>
            </svg>
        </div>
    </div>
</body>
</html>